using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using SmaTrendFollower.Extensions;
using SmaTrendFollower.Services;
using Alpaca.Markets;

namespace SmaTrendFollower.Examples;

/// <summary>
/// Demonstrates the enum conflict resolution system and SEC/Event filtering capabilities.
/// Shows how to safely use enums without conflicts and integrate event-based trading decisions.
/// </summary>
public class EnumConflictResolutionExample
{
    private readonly ILogger<EnumConflictResolutionExample> _logger;
    private readonly ISecEventFilterService _eventFilterService;

    public EnumConflictResolutionExample(
        ILogger<EnumConflictResolutionExample> logger,
        ISecEventFilterService eventFilterService)
    {
        _logger = logger;
        _eventFilterService = eventFilterService;
    }

    /// <summary>
    /// Demonstrates safe enum conversion between internal and external SDK enums.
    /// </summary>
    public void DemonstrateEnumConversion()
    {
        _logger.LogInformation("=== Enum Conversion Examples ===");

        // Example 1: Convert internal order side to Alpaca SDK enum
        var smaOrderSide = SmaOrderSide.Buy;
        var alpacaOrderSide = smaOrderSide.ToAlpacaOrderSide();
        
        _logger.LogInformation("Internal enum {SmaOrderSide} converts to Alpaca enum {AlpacaOrderSide}", 
            smaOrderSide, alpacaOrderSide);

        // Example 2: Convert Alpaca SDK enum back to internal enum
        var convertedBack = alpacaOrderSide.ToSmaOrderSide();
        _logger.LogInformation("Alpaca enum {AlpacaOrderSide} converts back to {SmaOrderSide}", 
            alpacaOrderSide, convertedBack);

        // Example 3: Order type conversions
        var smaOrderType = SmaOrderType.Limit;
        var alpacaOrderType = smaOrderType.ToAlpacaOrderType();
        _logger.LogInformation("Order type conversion: {SmaOrderType} -> {AlpacaOrderType}", 
            smaOrderType, alpacaOrderType);

        // Example 4: Using descriptions
        _logger.LogInformation("Order side description: {Description}", smaOrderSide.GetDescription());
        _logger.LogInformation("Order type description: {Description}", smaOrderType.GetDescription());
    }

    /// <summary>
    /// Demonstrates SEC filing analysis and impact assessment.
    /// </summary>
    public void DemonstrateSecFilingAnalysis()
    {
        _logger.LogInformation("=== SEC Filing Analysis Examples ===");

        // Example 1: Analyze different SEC filing types
        var filingTypes = new[]
        {
            SmaSecFilingType.Form8K,
            SmaSecFilingType.Form10K,
            SmaSecFilingType.Form10Q,
            SmaSecFilingType.FormS1
        };

        foreach (var filingType in filingTypes)
        {
            var impactLevel = filingType.ToImpactLevel();
            var needsAttention = filingType.RequiresImmediateAttention();
            var description = filingType.GetDescription();

            _logger.LogInformation("Filing: {Description} | Impact: {ImpactLevel} | Immediate Attention: {NeedsAttention}",
                description, impactLevel.GetDescription(), needsAttention);
        }

        // Example 2: Create and analyze a SEC filing
        var filing = new SecFiling
        {
            Symbol = "AAPL",
            FilingType = SmaSecFilingType.Form8K,
            FilingDate = DateTime.UtcNow.AddHours(-2),
            Description = "Current Report - Material Agreement",
            Url = "https://sec.gov/example/8k"
        };

        _logger.LogInformation("Created SEC filing for {Symbol}: {Description} filed at {FilingDate}",
            filing.Symbol, filing.Description, filing.FilingDate);
    }

    /// <summary>
    /// Demonstrates market event analysis and trading recommendations.
    /// </summary>
    public void DemonstrateMarketEventAnalysis()
    {
        _logger.LogInformation("=== Market Event Analysis Examples ===");

        // Example 1: Analyze different market event types
        var eventTypes = new[]
        {
            SmaMarketEventType.EarningsAnnouncement,
            SmaMarketEventType.FdaApproval,
            SmaMarketEventType.MergerAcquisition,
            SmaMarketEventType.DividendDeclaration
        };

        foreach (var eventType in eventTypes)
        {
            var impactLevel = eventType.ToImpactLevel();
            var causesVolatility = eventType.CausesHighVolatility();
            var description = eventType.GetDescription();

            _logger.LogInformation("Event: {Description} | Impact: {ImpactLevel} | High Volatility: {CausesVolatility}",
                description, impactLevel.GetDescription(), causesVolatility);
        }

        // Example 2: Create and analyze market events
        var events = new[]
        {
            new MarketEvent
            {
                Symbol = "AAPL",
                EventType = SmaMarketEventType.EarningsAnnouncement,
                EventDate = DateTime.UtcNow.AddDays(2),
                ImpactLevel = SmaEventImpactLevel.High,
                Description = "Q4 Earnings Release"
            },
            new MarketEvent
            {
                Symbol = "MRNA",
                EventType = SmaMarketEventType.FdaApproval,
                EventDate = DateTime.UtcNow.AddDays(1),
                ImpactLevel = SmaEventImpactLevel.Critical,
                Description = "FDA Decision on New Vaccine"
            }
        };

        foreach (var marketEvent in events)
        {
            _logger.LogInformation("Market Event: {Symbol} - {Description} on {EventDate} (Impact: {ImpactLevel})",
                marketEvent.Symbol, marketEvent.Description, marketEvent.EventDate, 
                marketEvent.ImpactLevel.GetDescription());
        }
    }

    /// <summary>
    /// Demonstrates event-based trading decision making.
    /// </summary>
    public async Task DemonstrateTradingDecisions()
    {
        _logger.LogInformation("=== Event-Based Trading Decisions ===");

        var symbols = new[] { "AAPL", "MSFT", "GOOGL", "TSLA" };

        foreach (var symbol in symbols)
        {
            // Example 1: Check for high-impact events
            var hasHighImpact = await _eventFilterService.HasHighImpactEventsAsync(symbol, SmaEventTimePeriod.Next3Days);
            _logger.LogInformation("{Symbol} has high-impact events in next 3 days: {HasHighImpact}", symbol, hasHighImpact);

            // Example 2: Get trading recommendation with earnings filter
            var recommendation = await _eventFilterService.GetTradingRecommendationAsync(symbol, SmaEventFilterType.ExcludeEarnings);
            
            _logger.LogInformation("Trading recommendation for {Symbol}:", symbol);
            _logger.LogInformation("  Action: {Action}", recommendation.Action.GetDescription());
            _logger.LogInformation("  Reason: {Reason}", recommendation.Reason);
            _logger.LogInformation("  Confidence: {Confidence:P1}", recommendation.Confidence);
            _logger.LogInformation("  Position Size Multiplier: {Multiplier:P0}", recommendation.RecommendedPositionSizeMultiplier);

            // Example 3: Apply trading decision logic
            await ApplyTradingDecision(symbol, recommendation);
        }
    }

    /// <summary>
    /// Demonstrates position sizing based on event impact.
    /// </summary>
    public void DemonstratePositionSizing()
    {
        _logger.LogInformation("=== Event-Based Position Sizing ===");

        var baseQuantity = 100m;
        var impactLevels = Enum.GetValues<SmaEventImpactLevel>();

        foreach (var impactLevel in impactLevels)
        {
            var multiplier = impactLevel.ToPositionSizeMultiplier();
            var adjustedQuantity = baseQuantity * multiplier;

            _logger.LogInformation("Impact Level: {ImpactLevel} | Multiplier: {Multiplier:P0} | Quantity: {BaseQuantity} -> {AdjustedQuantity}",
                impactLevel.GetDescription(), multiplier, baseQuantity, adjustedQuantity);
        }
    }

    /// <summary>
    /// Demonstrates enum validation and safe parsing.
    /// </summary>
    public void DemonstrateEnumValidation()
    {
        _logger.LogInformation("=== Enum Validation Examples ===");

        // Example 1: Validate enum values
        var validEnum = SmaEventImpactLevel.High;
        var invalidEnum = (SmaEventImpactLevel)999;

        _logger.LogInformation("Valid enum {ValidEnum} is valid: {IsValid}", validEnum, validEnum.IsValidEnumValue());
        _logger.LogInformation("Invalid enum {InvalidEnum} is valid: {IsValid}", invalidEnum, invalidEnum.IsValidEnumValue());

        // Example 2: Safe enum parsing with defaults
        var defaultValue = SmaEventImpactLevel.Unknown;
        var safeValid = validEnum.GetSafeEnumValue(defaultValue);
        var safeInvalid = invalidEnum.GetSafeEnumValue(defaultValue);

        _logger.LogInformation("Safe parsing - Valid: {ValidEnum} -> {SafeValid}", validEnum, safeValid);
        _logger.LogInformation("Safe parsing - Invalid: {InvalidEnum} -> {SafeInvalid}", invalidEnum, safeInvalid);

        // Example 3: Parse from description
        try
        {
            var parsedEnum = EnumExtensions.ParseFromDescription<SmaEventImpactLevel>("High Impact");
            _logger.LogInformation("Parsed from description 'High Impact': {ParsedEnum}", parsedEnum);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing enum from description");
        }
    }

    /// <summary>
    /// Demonstrates comprehensive enum metadata usage.
    /// </summary>
    public void DemonstrateEnumMetadata()
    {
        _logger.LogInformation("=== Enum Metadata Examples ===");

        // Example 1: Get all descriptions for an enum type
        var impactDescriptions = EnumExtensions.GetEnumDescriptions<SmaEventImpactLevel>();
        _logger.LogInformation("All SmaEventImpactLevel descriptions:");
        foreach (var (level, description) in impactDescriptions)
        {
            _logger.LogInformation("  {Level} = {Description}", level, description);
        }

        // Example 2: Get all descriptions for trading actions
        var actionDescriptions = EnumExtensions.GetEnumDescriptions<SmaEventTradingAction>();
        _logger.LogInformation("All SmaEventTradingAction descriptions:");
        foreach (var (action, description) in actionDescriptions)
        {
            var allowsNewPositions = action.AllowsNewPositions();
            var stopMultiplier = action.ToStopLossMultiplier();
            
            _logger.LogInformation("  {Action} = {Description} | Allows New Positions: {AllowsNew} | Stop Multiplier: {StopMultiplier:P0}",
                action, description, allowsNewPositions, stopMultiplier);
        }
    }

    /// <summary>
    /// Applies trading decision based on event recommendation.
    /// </summary>
    private async Task ApplyTradingDecision(string symbol, EventTradingRecommendation recommendation)
    {
        switch (recommendation.Action)
        {
            case SmaEventTradingAction.ContinueNormal:
                _logger.LogInformation("  -> Continue normal trading for {Symbol}", symbol);
                break;

            case SmaEventTradingAction.ReducePositionSize:
                _logger.LogInformation("  -> Reduce position size for {Symbol} by {Reduction:P0}", 
                    symbol, 1 - recommendation.RecommendedPositionSizeMultiplier);
                break;

            case SmaEventTradingAction.AvoidNewPositions:
                _logger.LogInformation("  -> Avoid new positions in {Symbol}", symbol);
                break;

            case SmaEventTradingAction.CloseExistingPositions:
                _logger.LogInformation("  -> Close existing positions in {Symbol}", symbol);
                break;

            case SmaEventTradingAction.ApplyTighterStops:
                var stopMultiplier = recommendation.Action.ToStopLossMultiplier();
                _logger.LogInformation("  -> Apply tighter stops for {Symbol} (multiplier: {StopMultiplier:P0})", 
                    symbol, stopMultiplier);
                break;

            case SmaEventTradingAction.IncreaseMonitoring:
                _logger.LogInformation("  -> Increase monitoring for {Symbol}", symbol);
                break;

            default:
                _logger.LogInformation("  -> No specific action for {Symbol}", symbol);
                break;
        }

        await Task.Delay(10); // Simulate async operation
    }

    /// <summary>
    /// Runs all demonstration examples.
    /// </summary>
    public async Task RunAllExamples()
    {
        _logger.LogInformation("Starting Enum Conflict Resolution Examples...");

        DemonstrateEnumConversion();
        DemonstrateSecFilingAnalysis();
        DemonstrateMarketEventAnalysis();
        await DemonstrateTradingDecisions();
        DemonstratePositionSizing();
        DemonstrateEnumValidation();
        DemonstrateEnumMetadata();

        _logger.LogInformation("Enum Conflict Resolution Examples completed successfully!");
    }
}
